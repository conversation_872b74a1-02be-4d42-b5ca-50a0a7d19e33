import app from 'flarum/admin/app';

interface SettingConfig {
    setting: string;
    type: string;
    label: string;
}

interface LinkImagePair {
    index: number;
    linkKey: string;
    imageKey: string;
}

export class AdminSettingsConfig {
    private static readonly EXTENSION_PREFIX = 'wusong8899-client1-header-adv';
    private static readonly TRANSLATION_PREFIX = 'wusong8899-client1.admin';

    /**
     * Generate link and image pairs for the specified range
     */
    private static generateLinkImagePairs(start: number, end: number): LinkImagePair[] {
        const pairs: LinkImagePair[] = [];
        for (let i = start; i <= end; i++) {
            pairs.push({
                index: i,
                linkKey: `Link${i}`,
                imageKey: `Image${i}`
            });
        }
        return pairs;
    }

    /**
     * Create a setting configuration object
     */
    private static createSetting(key: string, type: string, translationKey: string): SettingConfig {
        return {
            setting: `${this.EXTENSION_PREFIX}.${key}`,
            type: type,
            label: app.translator.trans(`${this.TRANSLATION_PREFIX}.${translationKey}`)
        };
    }

    /**
     * Get all basic settings (non-repetitive ones)
     */
    private static getBasicSettings(): SettingConfig[] {
        return [
            this.createSetting('TransitionTime', 'number', 'TransitionTime')
        ];
    }

    /**
     * Get all link and image settings
     */
    private static getLinkImageSettings(): SettingConfig[] {
        const settings: SettingConfig[] = [];
        const pairs = this.generateLinkImagePairs(1, 30);

        pairs.forEach(pair => {
            // Add link setting
            settings.push(this.createSetting(pair.linkKey, 'URL', pair.linkKey));
            // Add image setting
            settings.push(this.createSetting(pair.imageKey, 'URL', pair.imageKey));
        });

        return settings;
    }

    /**
     * Get all settings for the extension
     */
    public static getAllSettings(): SettingConfig[] {
        return [
            ...this.getBasicSettings(),
            ...this.getLinkImageSettings()
        ];
    }

    /**
     * Get settings by category
     */
    public static getSettingsByCategory(category: 'basic' | 'links'): SettingConfig[] {
        switch (category) {
            case 'basic':
                return this.getBasicSettings();
            case 'links':
                return this.getLinkImageSettings();
            default:
                return [];
        }
    }

    /**
     * Get the number of link/image pairs
     */
    public static getLinkImagePairCount(): number {
        return 30;
    }
}
