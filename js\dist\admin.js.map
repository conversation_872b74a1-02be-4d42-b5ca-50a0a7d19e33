{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,mBCAlF,MAAM,EAA+BI,OAAOC,KAAKC,OAAY,I,aCchDC,EAAmB,oBAAAA,IAAA,CAqF3B,OAjFDA,EAGeC,uBAAf,SAAsCC,EAAeC,GAEjD,IADA,IAAMC,EAAyB,GACtBC,EAAIH,EAAOG,GAAKF,EAAKE,IAC1BD,EAAME,KAAK,CACPC,MAAOF,EACPG,QAAS,OAAOH,EAChBI,SAAU,QAAQJ,IAG1B,OAAOD,CACX,EAEAJ,EAGeU,cAAf,SAA6BxB,EAAayB,EAAcC,GACpD,MAAO,CACHC,QAAYC,KAAKC,iBAAgB,IAAI7B,EACrCyB,KAAMA,EACNK,MAAOC,IAAAA,WAAeC,MAASJ,KAAKK,mBAAkB,IAAIP,GAElE,EAEAZ,EAGeoB,iBAAf,WACI,MAAO,CACHN,KAAKJ,cAAc,iBAAkB,SAAU,kBAEvD,EAEAV,EAGeqB,qBAAf,WAAuD,IAAAC,EAAA,KAC7CC,EAA4B,GAUlC,OATcT,KAAKb,uBAAuB,EAAG,IAEvCuB,QAAQ,SAAAC,GAEVF,EAASjB,KAAKgB,EAAKZ,cAAce,EAAKjB,QAAS,MAAOiB,EAAKjB,UAE3De,EAASjB,KAAKgB,EAAKZ,cAAce,EAAKhB,SAAU,MAAOgB,EAAKhB,UAChE,GAEOc,CACX,EAEAvB,EAGc0B,eAAd,WACI,MAAO,GAAPC,OACOb,KAAKM,mBACLN,KAAKO,uBAEhB,EAEArB,EAGc4B,sBAAd,SAAoCC,GAChC,OAAQA,GACJ,IAAK,QACD,OAAOf,KAAKM,mBAChB,IAAK,QACD,OAAON,KAAKO,uBAChB,QACI,MAAO,GAEnB,EAEArB,EAGc8B,sBAAd,WACI,OAAO,EACX,EAAC9B,CAAA,CArF2B,GAAnBA,EACee,iBAAmB,gCADlCf,EAEemB,mBAAqB,2BCbjDF,IAAAA,aAAiBc,IAAI,gCAAiC,WAClD,IAAMC,EAAgBf,IAAAA,cAAiB,IAAK,iCAG5CjB,EAAoB0B,iBAAiBF,QAAQ,SAAAX,GACzCmB,EAAcC,gBAAgBpB,EAClC,EACJ,E", "sources": ["webpack://@justoverclock/header-slideshow/webpack/bootstrap", "webpack://@justoverclock/header-slideshow/webpack/runtime/compat get default export", "webpack://@justoverclock/header-slideshow/webpack/runtime/define property getters", "webpack://@justoverclock/header-slideshow/webpack/runtime/hasOwnProperty shorthand", "webpack://@justoverclock/header-slideshow/external root \"flarum.core.compat['app']\"", "webpack://@justoverclock/header-slideshow/./src/admin/config/settings-config.ts", "webpack://@justoverclock/header-slideshow/./src/admin/index.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['app'];", "import app from 'flarum/app';\n\ninterface SettingConfig {\n    setting: string;\n    type: string;\n    label: string;\n}\n\ninterface LinkImagePair {\n    index: number;\n    linkKey: string;\n    imageKey: string;\n}\n\nexport class AdminSettingsConfig {\n    private static readonly EXTENSION_PREFIX = 'wusong8899-client1-header-adv';\n    private static readonly TRANSLATION_PREFIX = 'wusong8899-client1.admin';\n    \n    /**\n     * Generate link and image pairs for the specified range\n     */\n    private static generateLinkImagePairs(start: number, end: number): LinkImagePair[] {\n        const pairs: LinkImagePair[] = [];\n        for (let i = start; i <= end; i++) {\n            pairs.push({\n                index: i,\n                linkKey: `Link${i}`,\n                imageKey: `Image${i}`\n            });\n        }\n        return pairs;\n    }\n\n    /**\n     * Create a setting configuration object\n     */\n    private static createSetting(key: string, type: string, translationKey: string): SettingConfig {\n        return {\n            setting: `${this.EXTENSION_PREFIX}.${key}`,\n            type: type,\n            label: app.translator.trans(`${this.TRANSLATION_PREFIX}.${translationKey}`)\n        };\n    }\n\n    /**\n     * Get all basic settings (non-repetitive ones)\n     */\n    private static getBasicSettings(): SettingConfig[] {\n        return [\n            this.createSetting('TransitionTime', 'number', 'TransitionTime')\n        ];\n    }\n\n    /**\n     * Get all link and image settings\n     */\n    private static getLinkImageSettings(): SettingConfig[] {\n        const settings: SettingConfig[] = [];\n        const pairs = this.generateLinkImagePairs(1, 30);\n        \n        pairs.forEach(pair => {\n            // Add link setting\n            settings.push(this.createSetting(pair.linkKey, 'URL', pair.linkKey));\n            // Add image setting\n            settings.push(this.createSetting(pair.imageKey, 'URL', pair.imageKey));\n        });\n        \n        return settings;\n    }\n\n    /**\n     * Get all settings for the extension\n     */\n    public static getAllSettings(): SettingConfig[] {\n        return [\n            ...this.getBasicSettings(),\n            ...this.getLinkImageSettings()\n        ];\n    }\n\n    /**\n     * Get settings by category\n     */\n    public static getSettingsByCategory(category: 'basic' | 'links'): SettingConfig[] {\n        switch (category) {\n            case 'basic':\n                return this.getBasicSettings();\n            case 'links':\n                return this.getLinkImageSettings();\n            default:\n                return [];\n        }\n    }\n\n    /**\n     * Get the number of link/image pairs\n     */\n    public static getLinkImagePairCount(): number {\n        return 30;\n    }\n}\n", "import app from 'flarum/app';\nimport { AdminSettingsConfig } from './config/settings-config';\n\napp.initializers.add('wusong8899/client1-header-adv', () => {\n    const extensionData = app.extensionData.for('wusong8899-client1-header-adv');\n\n    // Register all settings from configuration\n    AdminSettingsConfig.getAllSettings().forEach(setting => {\n        extensionData.registerSetting(setting);\n    });\n});\n\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "flarum", "core", "compat", "AdminSettingsConfig", "generateLinkImagePairs", "start", "end", "pairs", "i", "push", "index", "linkKey", "image<PERSON>ey", "createSetting", "type", "<PERSON><PERSON><PERSON>", "setting", "this", "EXTENSION_PREFIX", "label", "app", "trans", "TRANSLATION_PREFIX", "getBasicSettings", "getLinkImageSettings", "_this", "settings", "for<PERSON>ach", "pair", "getAllSettings", "concat", "getSettingsByCategory", "category", "getLinkImagePairCount", "add", "extensionData", "registerSetting"], "sourceRoot": ""}